#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试预览功能
"""

import sys
import os
from pathlib import Path

# 添加src目录到Python路径
sys.path.insert(0, str(Path(__file__).parent / "src"))

from PyQt6.QtWidgets import QApplication
from ui.preview_dialog import PreviewDialog
from utils.config_manager import Config<PERSON>anager

def test_preview_dialog():
    """测试预览对话框"""
    app = QApplication(sys.argv)
    
    # 创建配置管理器
    config_manager = ConfigManager()
    
    # 创建测试文件信息
    test_file_info = {
        'name': '测试图片.png',
        'path': str(Path(__file__).parent / 'README.md'),  # 使用README作为测试文件
        'type': 'md',
        'category': '其他',
        'is_folder': False
    }
    
    # 创建并显示预览对话框
    preview_dialog = PreviewDialog(test_file_info, config_manager)
    preview_dialog.show()
    
    sys.exit(app.exec())

if __name__ == "__main__":
    test_preview_dialog()
