# -*- coding: utf-8 -*-
"""
数据库管理器 - 负责管理SQLite数据库操作
"""

import sqlite3
import os
from datetime import datetime, timedelta
from pathlib import Path
from typing import List, Dict, Any, Optional


class DatabaseManager:
    """数据库管理器类"""
    
    def __init__(self, db_path: str):
        """初始化数据库管理器"""
        self.db_path = db_path
        self.init_database()
    
    def init_database(self) -> None:
        """初始化数据库表结构"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            
            # 创建文件表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS Files (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    name TEXT NOT NULL,
                    path TEXT NOT NULL UNIQUE,
                    category TEXT NOT NULL,
                    type TEXT NOT NULL,
                    creation_time DATETIME DEFAULT CURRENT_TIMESTAMP,
                    modified_time DATETIME DEFAULT CURRENT_TIMESTAMP,
                    custom_color TEXT,
                    original_path TEXT,
                    delete_time DATETIME,
                    is_deleted INTEGER DEFAULT 0
                )
            ''')
            
            # 创建文件夹表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS Folders (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    name TEXT NOT NULL,
                    path TEXT NOT NULL UNIQUE,
                    category TEXT NOT NULL,
                    custom_color TEXT,
                    custom_thumbnail TEXT,
                    creation_time DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # 创建索引
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_files_category ON Files(category)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_files_path ON Files(path)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_folders_category ON Folders(category)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_folders_path ON Folders(path)')
            
            conn.commit()
    
    def add_file(self, name: str, path: str, category: str, file_type: str,
                 custom_color: Optional[str] = None) -> int:
        """添加文件记录"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute('''
                INSERT INTO Files (name, path, category, type, custom_color)
                VALUES (?, ?, ?, ?, ?)
            ''', (name, path, category, file_type, custom_color))
            conn.commit()
            return cursor.lastrowid or 0
    
    def add_folder(self, name: str, path: str, category: str,
                   custom_color: Optional[str] = None, custom_thumbnail: Optional[str] = None) -> int:
        """添加文件夹记录"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute('''
                INSERT INTO Folders (name, path, category, custom_color, custom_thumbnail)
                VALUES (?, ?, ?, ?, ?)
            ''', (name, path, category, custom_color, custom_thumbnail))
            conn.commit()
            return cursor.lastrowid or 0

    def get_files_by_category(self, category: str, include_deleted: bool = False) -> List[Dict[str, Any]]:
        """根据分类获取文件列表"""
        with sqlite3.connect(self.db_path) as conn:
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()
            
            if include_deleted:
                cursor.execute('SELECT * FROM Files WHERE category = ?', (category,))
            else:
                cursor.execute('SELECT * FROM Files WHERE category = ? AND is_deleted = 0', (category,))
            
            return [dict(row) for row in cursor.fetchall()]
    
    def get_folders_by_category(self, category: str) -> List[Dict[str, Any]]:
        """根据分类获取文件夹列表"""
        with sqlite3.connect(self.db_path) as conn:
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()
            cursor.execute('SELECT * FROM Folders WHERE category = ?', (category,))
            return [dict(row) for row in cursor.fetchall()]

    def get_deleted_files(self) -> List[Dict[str, Any]]:
        """获取回收站中的文件（已删除的文件）"""
        with sqlite3.connect(self.db_path) as conn:
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()
            cursor.execute('SELECT * FROM Files WHERE is_deleted = 1 ORDER BY delete_time DESC')
            return [dict(row) for row in cursor.fetchall()]

    def search_files(self, query: str, category: Optional[str] = None) -> List[Dict[str, Any]]:
        """搜索文件"""
        with sqlite3.connect(self.db_path) as conn:
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()
            
            if category:
                cursor.execute('''
                    SELECT * FROM Files 
                    WHERE category = ? AND name LIKE ? AND is_deleted = 0
                    ORDER BY name
                ''', (category, f'%{query}%'))
            else:
                cursor.execute('''
                    SELECT * FROM Files 
                    WHERE name LIKE ? AND is_deleted = 0
                    ORDER BY name
                ''', (f'%{query}%',))
            
            return [dict(row) for row in cursor.fetchall()]

    def search_files_by_type(self, file_type: str, category: Optional[str] = None) -> List[Dict[str, Any]]:
        """按文件类型搜索文件"""
        with sqlite3.connect(self.db_path) as conn:
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()

            # 处理文件类型查询（移除可能的点号）
            file_type = file_type.strip().lstrip('.')

            if category:
                cursor.execute('''
                    SELECT * FROM Files
                    WHERE category = ? AND type LIKE ? AND is_deleted = 0
                    ORDER BY name
                ''', (category, f'%{file_type}%'))
            else:
                cursor.execute('''
                    SELECT * FROM Files
                    WHERE type LIKE ? AND is_deleted = 0
                    ORDER BY name
                ''', (f'%{file_type}%',))

            return [dict(row) for row in cursor.fetchall()]

    def update_file_location(self, file_id: int, new_path: str, new_category: str, new_subfolder: str = "") -> bool:
        """更新文件位置信息"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    UPDATE Files
                    SET path = ?, category = ?, subfolder = ?, modified_time = CURRENT_TIMESTAMP
                    WHERE id = ?
                ''', (new_path, new_category, new_subfolder, file_id))

                return cursor.rowcount > 0
        except Exception as e:
            print(f"更新文件位置失败: {e}")
            return False

    def delete_file(self, file_id: int) -> bool:
        """删除文件（移至回收站）"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            
            # 获取文件信息
            cursor.execute('SELECT * FROM Files WHERE id = ?', (file_id,))
            file_info = cursor.fetchone()
            
            if file_info:
                # 更新文件状态为已删除
                cursor.execute('''
                    UPDATE Files 
                    SET is_deleted = 1, delete_time = ?, original_path = ?
                    WHERE id = ?
                ''', (datetime.now().isoformat(), file_info[2], file_id))
                conn.commit()
                return True
            
            return False
    
    def restore_file(self, file_id: int) -> bool:
        """从回收站还原文件"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute('''
                UPDATE Files 
                SET is_deleted = 0, delete_time = NULL
                WHERE id = ?
            ''', (file_id,))
            conn.commit()
            return cursor.rowcount > 0
    
    def permanently_delete_file(self, file_id: int) -> bool:
        """永久删除文件"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute('DELETE FROM Files WHERE id = ?', (file_id,))
            conn.commit()
            return cursor.rowcount > 0
    
    def get_deleted_files(self) -> List[Dict[str, Any]]:
        """获取回收站中的文件"""
        with sqlite3.connect(self.db_path) as conn:
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()
            cursor.execute('SELECT * FROM Files WHERE is_deleted = 1 ORDER BY delete_time DESC')
            return [dict(row) for row in cursor.fetchall()]
    
    def cleanup_old_deleted_files(self, days: int = 30) -> int:
        """清理超过指定天数的已删除文件"""
        cutoff_date = datetime.now() - timedelta(days=days)
        
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute('''
                DELETE FROM Files 
                WHERE is_deleted = 1 AND delete_time < ?
            ''', (cutoff_date.isoformat(),))
            conn.commit()
            return cursor.rowcount
    
    def update_file_color(self, file_id: int, color: str) -> bool:
        """更新文件颜色"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute('UPDATE Files SET custom_color = ? WHERE id = ?', (color, file_id))
            conn.commit()
            return cursor.rowcount > 0
    
    def update_folder_color(self, folder_id: int, color: str) -> bool:
        """更新文件夹颜色"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute('UPDATE Folders SET custom_color = ? WHERE id = ?', (color, folder_id))
            conn.commit()
            return cursor.rowcount > 0
