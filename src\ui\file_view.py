# -*- coding: utf-8 -*-
"""
文件视图 - 中间文件显示区域组件
"""

from PyQt6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QGridLayout,
                            QScrollArea, QLabel, QPushButton, QFileDialog,
                            QMessageBox, QMenu, QApplication)
from PyQt6.QtCore import Qt, pyqtSignal, QPoint
from PyQt6.QtGui import QPixmap, QFont, QDragEnterEvent, QDropEvent, QContextMenuEvent, QMouseEvent

from models.database import DatabaseManager
from utils.config_manager import ConfigManager
from utils.file_operations import FileOperations
from utils.thumbnail import ThumbnailGenerator
from utils.drag_drop import DragDropManager
from ui.rename_dialog import RenameDialog
from ui.preview_dialog import PreviewDialog
import os
import time
from pathlib import Path


class FileItemWidget(QWidget):
    """文件项组件"""

    # 信号
    clicked = pyqtSignal(dict)  # 点击信号
    double_clicked = pyqtSignal(dict)  # 双击信号

    def __init__(self, file_info: dict, thumbnail_generator: ThumbnailGenerator,
                 drag_drop_manager: DragDropManager, thumbnail_size: int = 100):
        super().__init__()
        self.file_info = file_info
        self.thumbnail_generator = thumbnail_generator
        self.drag_drop_manager = drag_drop_manager
        self.thumbnail_size = thumbnail_size
        self.selected = False
        self.drag_start_position = QPoint()

        self.init_ui()

    def init_ui(self):
        """初始化UI"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(5, 5, 5, 5)
        layout.setSpacing(5)

        # 缩略图
        self.thumbnail_label = QLabel()
        self.thumbnail_label.setFixedSize(self.thumbnail_size, self.thumbnail_size)
        self.thumbnail_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.thumbnail_label.setStyleSheet("""
            QLabel {
                border: 2px solid #5a5a5a;
                border-radius: 5px;
                background-color: #4a4a4a;
            }
        """)

        # 加载缩略图
        self.load_thumbnail()
        layout.addWidget(self.thumbnail_label)

        # 文件名
        self.name_label = QLabel(self.file_info.get('name', '未知'))
        self.name_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.name_label.setWordWrap(True)
        self.name_label.setMaximumWidth(self.thumbnail_size + 10)
        self.name_label.setFont(QFont("Microsoft YaHei", 9))
        layout.addWidget(self.name_label)

        # 设置固定大小
        self.setFixedSize(self.thumbnail_size + 20, self.thumbnail_size + 50)

    def load_thumbnail(self):
        """加载缩略图"""
        file_path = self.file_info.get('path', '')

        if file_path and os.path.exists(file_path):
            # 生成缩略图
            thumbnail_path = self.thumbnail_generator.generate_thumbnail(file_path)

            if thumbnail_path and os.path.exists(thumbnail_path):
                # 加载缩略图
                pixmap = QPixmap(thumbnail_path)
                if not pixmap.isNull():
                    # 缩放到合适大小
                    scaled_pixmap = pixmap.scaled(
                        self.thumbnail_size - 4, self.thumbnail_size - 4,
                        Qt.AspectRatioMode.KeepAspectRatio,
                        Qt.TransformationMode.SmoothTransformation
                    )
                    self.thumbnail_label.setPixmap(scaled_pixmap)
                    return

        # 如果无法加载缩略图，显示默认图标
        self._show_default_icon()

    def _show_default_icon(self):
        """显示默认图标"""
        file_type = self.file_info.get('type', '').lower()

        if file_type in ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp']:
            icon = "🖼️"
        elif file_type in ['mp4', 'avi', 'mov', 'mkv', 'wmv']:
            icon = "🎬"
        elif file_type in ['mp3', 'wav', 'flac', 'aac', 'ogg']:
            icon = "🎵"
        elif file_type in ['txt', 'md', 'py', 'js', 'html', 'css']:
            icon = "📄"
        else:
            icon = "📁" if self.file_info.get('is_folder', False) else "📄"

        self.thumbnail_label.setText(icon)
        self.thumbnail_label.setStyleSheet("""
            QLabel {
                border: 2px solid #5a5a5a;
                border-radius: 5px;
                background-color: #4a4a4a;
                font-size: 32px;
                color: #ffffff;
            }
        """)
    
    def mousePressEvent(self, event: QMouseEvent):
        """鼠标按下事件"""
        if event.button() == Qt.MouseButton.LeftButton:
            self.drag_start_position = event.position().toPoint()
            self.clicked.emit(self.file_info)

    def mouseMoveEvent(self, event: QMouseEvent):
        """鼠标移动事件 - 处理拖拽"""
        if not (event.buttons() & Qt.MouseButton.LeftButton):
            return

        if ((event.position().toPoint() - self.drag_start_position).manhattanLength() <
            QApplication.startDragDistance()):
            return

        # 开始拖拽
        self.drag_drop_manager.start_drag(self, self.file_info)

    def mouseDoubleClickEvent(self, event: QMouseEvent):
        """鼠标双击事件"""
        if event.button() == Qt.MouseButton.LeftButton:
            self.double_clicked.emit(self.file_info)
    
    def set_selected(self, selected: bool):
        """设置选中状态"""
        self.selected = selected
        if selected:
            self.thumbnail_label.setStyleSheet("""
                QLabel {
                    border: 2px solid #0078d4;
                    border-radius: 5px;
                    background-color: #4a4a4a;
                    font-size: 32px;
                }
            """)
        else:
            self.thumbnail_label.setStyleSheet("""
                QLabel {
                    border: 2px solid #5a5a5a;
                    border-radius: 5px;
                    background-color: #4a4a4a;
                    font-size: 32px;
                }
            """)


class FileView(QWidget):
    """文件视图组件"""

    # 信号
    file_selected = pyqtSignal(dict)  # 文件选中信号
    statusbar_message = pyqtSignal(str)  # 状态栏消息信号
    
    def __init__(self, config_manager: ConfigManager, db_manager: DatabaseManager):
        super().__init__()
        self.config_manager = config_manager
        self.db_manager = db_manager
        self.file_operations = FileOperations(config_manager, db_manager)
        self.thumbnail_generator = ThumbnailGenerator(config_manager)
        self.drag_drop_manager = DragDropManager(config_manager, db_manager)

        self.current_category = ""
        self.current_path = ""
        self.current_subfolder = ""  # 当前子文件夹
        self.file_widgets = []
        self.selected_files = []

        self.init_ui()
        self.setup_drag_drop()
        self.connect_drag_drop_signals()
    
    def init_ui(self):
        """初始化用户界面"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(5, 5, 5, 5)
        
        # 路径导航栏
        nav_layout = QHBoxLayout()
        
        self.path_label = QLabel("当前位置: /")
        self.path_label.setFont(QFont("Microsoft YaHei", 10))
        nav_layout.addWidget(self.path_label)
        
        nav_layout.addStretch()
        
        # 视图切换按钮（预留）
        self.grid_view_btn = QPushButton("网格视图")
        self.grid_view_btn.setCheckable(True)
        self.grid_view_btn.setChecked(True)
        nav_layout.addWidget(self.grid_view_btn)
        
        layout.addLayout(nav_layout)
        
        # 文件显示区域
        self.scroll_area = QScrollArea()
        self.scroll_area.setWidgetResizable(True)
        self.scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)
        self.scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)
        
        # 文件容器
        self.file_container = QWidget()
        self.file_layout = QGridLayout(self.file_container)
        self.file_layout.setSpacing(10)
        
        self.scroll_area.setWidget(self.file_container)
        layout.addWidget(self.scroll_area)
        
        # 底部工具栏
        bottom_layout = QHBoxLayout()
        
        self.select_all_btn = QPushButton("全选")
        self.select_all_btn.clicked.connect(self.select_all)
        bottom_layout.addWidget(self.select_all_btn)
        
        self.rename_btn = QPushButton("批量重命名")
        self.rename_btn.clicked.connect(self.batch_rename)
        bottom_layout.addWidget(self.rename_btn)
        
        self.delete_btn = QPushButton("删除选中")
        self.delete_btn.clicked.connect(self.delete_selected)
        bottom_layout.addWidget(self.delete_btn)
        
        self.color_btn = QPushButton("更改颜色")
        self.color_btn.clicked.connect(self.change_color)
        bottom_layout.addWidget(self.color_btn)
        
        bottom_layout.addStretch()
        
        layout.addLayout(bottom_layout)
        
        # 应用样式
        self.apply_styles()
    
    def apply_styles(self):
        """应用样式表"""
        style = """
        QWidget {
            background-color: #2b2b2b;
            color: #ffffff;
        }
        
        QLabel {
            color: #ffffff;
            padding: 5px;
        }
        
        QPushButton {
            background-color: #4a4a4a;
            border: 1px solid #5a5a5a;
            border-radius: 3px;
            padding: 5px 10px;
            color: #ffffff;
        }
        
        QPushButton:hover {
            background-color: #5a5a5a;
        }
        
        QPushButton:pressed {
            background-color: #6a6a6a;
        }
        
        QScrollArea {
            border: 1px solid #5a5a5a;
            border-radius: 5px;
            background-color: #3c3c3c;
        }
        """
        self.setStyleSheet(style)
    
    def setup_drag_drop(self):
        """设置拖放功能"""
        self.setAcceptDrops(True)

    def connect_drag_drop_signals(self):
        """连接拖放信号"""
        self.drag_drop_manager.files_dropped.connect(self.on_files_dropped)
        self.drag_drop_manager.file_moved.connect(self.on_file_moved)

    def dragEnterEvent(self, event: QDragEnterEvent):
        """拖拽进入事件"""
        mime_data = event.mimeData()
        if mime_data and self.drag_drop_manager.can_accept_drop(mime_data, self.current_category):
            event.acceptProposedAction()
        else:
            event.ignore()

    def dropEvent(self, event: QDropEvent):
        """拖放事件"""
        mime_data = event.mimeData()
        if not mime_data:
            return

        # 获取拖放位置，检查是否拖放到文件夹上
        drop_position = event.position().toPoint()
        target_folder = self._get_folder_at_position(drop_position)

        target_subfolder = self.current_subfolder
        if target_folder:
            # 如果拖放到文件夹上，更新目标子文件夹路径
            if self.current_subfolder:
                target_subfolder = f"{self.current_subfolder}/{target_folder}"
            else:
                target_subfolder = target_folder

        # 处理外部文件拖放
        if mime_data.hasUrls():
            imported_files = self.drag_drop_manager.handle_external_drop(
                mime_data, self.current_category, target_subfolder
            )
            if imported_files:
                self.refresh_current_view()
                self.statusbar_message.emit(f"成功导入 {len(imported_files)} 个文件")

        # 处理内部文件拖放
        elif mime_data.hasFormat("application/x-file-info"):
            success = self.drag_drop_manager.handle_internal_drop(
                mime_data, self.current_category, target_subfolder
            )
            if success:
                self.refresh_current_view()
                self.statusbar_message.emit("文件移动成功")

        event.acceptProposedAction()

    def _get_folder_at_position(self, position: QPoint) -> str:
        """获取指定位置的文件夹名称"""
        # 遍历所有文件组件，检查位置是否在文件夹上
        for widget in self.file_widgets:
            if widget.geometry().contains(position):
                file_info = widget.file_info
                if file_info.get('is_folder', False):
                    return file_info.get('name', '')
        return ""

    def on_files_dropped(self, file_paths: list, category: str):
        """文件拖放完成处理"""
        print(f"文件拖放完成: {len(file_paths)} 个文件到 {category}")

    def on_file_moved(self, file_info: dict, source_category: str, target_category: str):
        """文件移动完成处理"""
        print(f"文件移动: {file_info['name']} 从 {source_category} 到 {target_category}")
    
    def set_category(self, category: str, subfolder: str = ""):
        """设置当前分类和子文件夹"""
        self.current_category = category
        self.current_subfolder = subfolder

        if subfolder:
            self.current_path = f"/{category}/{subfolder}"
        else:
            self.current_path = f"/{category}"

        self.path_label.setText(f"当前位置: {self.current_path}")
        self.refresh_current_view()
    
    def refresh_current_view(self):
        """刷新当前视图"""
        # 清除现有文件组件
        self.clear_file_widgets()

        if not self.current_category:
            return

        # 获取文件和文件夹列表
        items = []

        # 特殊处理回收站
        if self.current_category == "回收站":
            # 从数据库获取已删除的文件
            deleted_files = self.db_manager.get_deleted_files()
            for file_data in deleted_files:
                file_info = {
                    'id': file_data['id'],
                    'name': file_data['name'],
                    'path': file_data['path'],
                    'category': "回收站",
                    'type': file_data['type'],
                    'is_folder': False,
                    'creation_time': file_data.get('creation_time'),
                    'modified_time': file_data.get('modified_time'),
                    'delete_time': file_data.get('delete_time'),
                    'original_category': file_data.get('category')  # 原始分类
                }
                items.append(file_info)
        else:
            # 构建实际的文件系统路径
            storage_path = self.config_manager.get_storage_path()
            current_dir = storage_path / self.current_category

            if self.current_subfolder:
                current_dir = current_dir / self.current_subfolder

            if current_dir.exists():
                # 添加文件夹
                for item in current_dir.iterdir():
                    if item.is_dir():
                        folder_info = {
                            'id': 0,  # 文件夹暂时不存储在数据库中
                            'name': item.name,
                            'path': str(item),
                            'category': self.current_category,
                            'type': 'folder',
                            'is_folder': True,
                            'creation_time': None,
                            'modified_time': None
                        }
                        items.append(folder_info)

                # 添加文件
                for item in current_dir.iterdir():
                    if item.is_file():
                        file_info = {
                            'id': 0,  # 临时ID，实际应该从数据库获取
                            'name': item.name,
                            'path': str(item),
                            'category': self.current_category,
                            'type': item.suffix.lower().lstrip('.') or 'file',
                            'is_folder': False,
                            'creation_time': None,
                            'modified_time': None
                        }
                        items.append(file_info)

        # 显示文件和文件夹
        self.display_items(items)
    
    def clear_file_widgets(self):
        """清除文件组件"""
        for widget in self.file_widgets:
            widget.deleteLater()
        self.file_widgets.clear()
        self.selected_files.clear()
    
    def display_items(self, items: list):
        """显示文件和文件夹"""
        columns = max(1, (self.scroll_area.width() - 50) // 130)  # 计算列数

        for i, item in enumerate(items):
            row = i // columns
            col = i % columns

            file_widget = FileItemWidget(
                item,
                self.thumbnail_generator,
                self.drag_drop_manager,
                self.config_manager.settings["thumbnail_size"]
            )
            file_widget.clicked.connect(self.on_file_clicked)
            file_widget.double_clicked.connect(self.on_file_double_clicked)

            self.file_layout.addWidget(file_widget, row, col)
            self.file_widgets.append(file_widget)
    
    def on_file_clicked(self, file_info: dict):
        """文件点击事件"""
        # 清除其他选中状态
        for widget in self.file_widgets:
            widget.set_selected(False)

        # 设置当前选中
        sender = self.sender()
        if sender and isinstance(sender, FileItemWidget):
            sender.set_selected(True)
            self.selected_files = [file_info]
            self.file_selected.emit(file_info)
    
    def on_file_double_clicked(self, file_info: dict):
        """文件双击事件"""
        if file_info.get('is_folder', False):
            # 进入文件夹
            folder_name = file_info['name']
            if self.current_subfolder:
                new_subfolder = f"{self.current_subfolder}/{folder_name}"
            else:
                new_subfolder = folder_name

            self.set_category(self.current_category, new_subfolder)
        else:
            # 如果是文件，显示预览对话框
            preview_dialog = PreviewDialog(file_info, self.config_manager, self)
            preview_dialog.exec()
    
    def search_files(self, query: str):
        """搜索文件"""
        if not query.strip():
            self.refresh_current_view()
            return

        # 清除现有显示
        self.clear_file_widgets()

        # 搜索文件
        files = self.db_manager.search_files(query, self.current_category)
        self.display_items(files)

    def display_search_results(self, search_results: list):
        """显示搜索结果"""
        # 清除现有显示
        self.clear_file_widgets()

        # 更新路径显示
        self.path_label.setText(f"搜索结果: 找到 {len(search_results)} 个文件")

        # 显示搜索结果
        self.display_items(search_results)
    
    def import_files(self, file_paths: list):
        """导入文件"""
        if not self.current_category:
            QMessageBox.warning(self, "警告", "请先选择一个分类")
            return
        
        success_count = 0
        for file_path in file_paths:
            if self.file_operations.import_file(file_path, self.current_category):
                success_count += 1
        
        if success_count > 0:
            QMessageBox.information(self, "导入完成", f"成功导入 {success_count} 个文件")
            self.refresh_current_view()
        else:
            QMessageBox.warning(self, "导入失败", "没有文件被成功导入")
    
    def select_all(self):
        """全选"""
        for widget in self.file_widgets:
            widget.set_selected(True)
        self.selected_files = [widget.file_info for widget in self.file_widgets]
    
    def rename_selected(self):
        """重命名选中项"""
        if not self.selected_files:
            QMessageBox.information(self, "提示", "请先选择要重命名的文件")
            return

        # 打开重命名对话框
        dialog = RenameDialog(self.selected_files, self.config_manager, self.db_manager, self)
        dialog.files_renamed.connect(self.on_files_renamed)
        dialog.exec()

    def on_files_renamed(self, files: list):
        """文件重命名完成处理"""
        self.refresh_current_view()
        self.statusbar_message.emit(f"成功重命名 {len(files)} 个文件")
    
    def batch_rename(self):
        """批量重命名"""
        if not self.selected_files:
            QMessageBox.information(self, "提示", "请先选择要重命名的文件")
            return
        
        # 这里应该打开批量重命名对话框
        QMessageBox.information(self, "提示", "批量重命名功能开发中...")
    
    def delete_selected(self):
        """删除选中文件"""
        if not self.selected_files:
            QMessageBox.information(self, "提示", "请先选择要删除的文件")
            return
        
        reply = QMessageBox.question(self, "确认删除", 
                                   f"确定要删除选中的 {len(self.selected_files)} 个文件吗？",
                                   QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No)
        
        if reply == QMessageBox.StandardButton.Yes:
            success_count = 0
            for file_info in self.selected_files:
                if self.file_operations.delete_file(file_info['id']):
                    success_count += 1

            if success_count > 0:
                QMessageBox.information(self, "删除完成", f"已将 {success_count} 个文件移至回收站")
            else:
                QMessageBox.warning(self, "删除失败", "删除文件时发生错误")

            self.refresh_current_view()
    
    def change_color(self):
        """更改颜色"""
        if not self.selected_files:
            QMessageBox.information(self, "提示", "请先选择要更改颜色的文件")
            return
        
        # 这里应该打开颜色选择对话框
        QMessageBox.information(self, "提示", "颜色更改功能开发中...")
    
    def contextMenuEvent(self, event: QContextMenuEvent):
        """右键菜单事件"""
        menu = QMenu(self)
        
        # 添加菜单项
        import_action = menu.addAction("导入文件")
        if import_action:
            import_action.triggered.connect(self.show_import_dialog)

        if self.selected_files:
            menu.addSeparator()
            rename_action = menu.addAction("重命名")
            if rename_action:
                rename_action.triggered.connect(self.rename_selected)

            delete_action = menu.addAction("删除")
            if delete_action:
                delete_action.triggered.connect(self.delete_selected)

            color_action = menu.addAction("更改颜色")
            if color_action:
                color_action.triggered.connect(self.change_color)
        
        menu.exec(event.globalPos())
    
    def show_import_dialog(self):
        """显示导入对话框"""
        file_paths, _ = QFileDialog.getOpenFileNames(
            self, "选择要导入的文件", "", 
            "所有文件 (*);;图片文件 (*.jpg *.jpeg *.png *.gif *.bmp);;视频文件 (*.mp4 *.avi *.mov *.mkv)"
        )
        
        if file_paths:
            self.import_files(file_paths)
