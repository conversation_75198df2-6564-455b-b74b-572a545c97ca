帮我写一个软件，他的作用是管理剪辑素材的图片视频之类的，类似于可视化文件夹但是多一些功能，可以拖出去复制到别的软件里软件里面，自由拖动，软件界面使用pyqt6来写，现代化，美观，有轻微动画，扁平风，支持windows和mac系统，软件整体要适配大部分格式，

首先先讲解基础设定：后缀指代名称的后缀。类型指代图片，视频，音频，文本这类的

部分快捷键：
F2-重命名，ctrl+F-搜索栏，鼠标侧键来快速前进和后退，ctrl+Z-撤回，ctrl+A-全选，12345快速切换分类栏，alt+空格+x切换小窗模式
软件界面：
首先软件的界面整体是暗色，白字，整体界面是一个大框左侧有一列分类列表，人物，场景，道具，其他，回收站，其他的有点特殊我单独在功能介绍和具体功能介绍里讲
分类列表里面就是正常文件夹的功能，比如可以新建文件夹，这些的，然后里面要每个文件和文件夹有复选框，方便功能的批量处理，所有的文件和文件夹都要有预览图，双击可以放大，在点一下可以退出放大，在软件的右边有预览框可以收回和展开，就类似于windows文件夹里面右边的预览窗口，展开收起就类似打开关闭，然后在软件上方，和下方就是实际的功能栏，最上面从左到右是导入，搜索栏，导出，更改排序方式，全局设置，小窗模式，在下面的功能栏从左到右依次是，全选，批量重命名，删除选中，更改颜色，在回收站下面会多出批量还原

功能介绍：支持快捷键，有右键菜单，可以自定义文件夹和文件名称颜色，和分类栏颜色，默认是人物是绿色，场景是红色，道具是黄色，其他是蓝色，回收站是粉色，可以自由拖动文件位置，和跨分栏拖动，但是要注意有些地方无法拖入部分后缀文件，导出支持批量导出，用复选框打勾的方式，导入也同样支持批量导入，支持批量按照时间重命名自动加入后缀，如果导入有重复的会自动弹出预览图和让你重命名的窗口，其他这栏里面才可以放置音频，文本，视频这些，
分类列表里面，其中人物，场景，道具，只允许放置图片，其他这栏里面才可以放置音频，文本，视频这些，回收站是不允许更改
然后其他的快速预览也要做，回收站允许存放30天的文件，超过30天会自动永久删除，30天前可以随时还原
可以自定义文件夹的缩略图也可以选择使用文件夹里面的文件的图片，还有小窗模式。


具体功能介绍：
人物：默认人物里面会有以下文件夹。主角，路人，怪兽，其他
场景：默认场景里面会有以下文件夹。室内，室外，
道具：默认道具里面会有以下文件夹。武器，物品，载具，
其他：默认其他里面有以下文件夹。BGM，音效，视频，文本，其他，
回收站：不允许新建文件和文件夹，可以删除，下面的功能栏里面只允许以下功能，删除选中，还原选中，全选。
导入：点击导入之后可以选择导入哪个分栏，然后在去选中文件，记得一定要区分分栏类型
搜索：可以切换全局或者单独哪个分类里的进行搜索，模糊搜索的规则就类似windows的文件夹，也可以搜索文件类型比如图片， 视频，音效，文本这些会自动在主体界面展示搜索的文件及文件夹。
导出：点击导出会有小窗口，可以选中导出到哪个文件位置或者是剪印草稿位置，
更改排序方式：可以按照创建时间，后缀，类型，
全局设置：第一次打开软件会选中要保存的位置，可以更改位置。剪印草稿地址，改变缩略图大小。
批量重命名：可以快速把选中的重命名名称和后缀，后缀的顺序可以更改，比如按照创建时间，但是回收站不允许重命名，在人物那栏要不一样，要改成名称-表情-动作-后缀，都是可以 自由选中要不要的，名称是必须填写杠号会自动加的，比如只填写名称和表情就是男1-害羞。就是可以自由选填的。
删除选中：除了回收站的，其他分类栏的都会放入回收站，在回收站栏的会永久删除，点击删除前都要有确认提示防止误删。
批量还原：只允许在回收站有这个功能，可以快速还原到原来的位置
全选：就是全选也可以按ctrl+A
更改颜色：可以选定更改分类的颜色或者是更改选中文件夹和文件的颜色，只允许更改16种颜色，不允许黑色和与黑色相近的颜色，比如灰色
右键菜单：右键菜单里面有平常的复制，粘贴，重命名，导入，导出，搜索，删除选中，批量重命名，筛选，更改排序方式，更改颜色，其他里面的右键菜单都一样，回收站的右键菜单里面除了批量还原，筛选，搜索，更改排序方式，批量删除，其他以外的统统不允许
小窗模式：点击小窗模式后，会自动变成很小的一个窗口，方便拖入素材到其他软件，被置顶在屏幕最上方，窗口默认在右下角，会变成只有两列显示素材，没有分类栏，只有搜索栏只支持全局的，还有退出小窗的按钮，可以自由拖动这个窗口的位置。
